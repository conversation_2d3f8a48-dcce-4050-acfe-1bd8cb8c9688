import type { Metada<PERSON> } from "next";
import { redirect } from 'next/navigation'
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: `${process.env.NAME} - Register`,
    description: "Create a new account."
};

export default async function Layout_Register(_: Readonly<{
    children: React.ReactNode;
}>) {

    // - AUTH - //

    const session = await auth();
    if (session) {
        redirect("/account");
    }


    // - MAIN - //

    return _.children;

}

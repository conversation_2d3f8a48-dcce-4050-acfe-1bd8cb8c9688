"use client"

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function Page_LoginRegister() {

    const router = useRouter();

    // - EFFECTS - //

    useEffect(() => {
        // Redirect to the new login page
        router.replace("/login");
    }, [router]);

    // - Main - //

    return null;

                        {/* Form */}
                        <JC_Form
                            key={errorMessage}
                            submitButtonText="Register"
                            onSubmit={register}
                            isLoading={isLoading}
                            errorMessage={errorMessage}
                            fields={[
                                // First Name
                                {
                                    ...D_FieldModel_FirstName(),
                                    inputId:"login-register-first-input",
                                    onChange: (newValue) => setRegisterFirstName(newValue),
                                    value: registerFirstName
                                },
                                // Last Name
                                {
                                    ...D_FieldModel_LastName(),
                                    inputId:"register-last-name-input",
                                    onChange: (newValue) => setRegisterLastName(newValue),
                                    value: registerLastName
                                },
                                // Email
                                {
                                    ...D_FieldModel_Email(),
                                    inputId:"register-email-input",
                                    onChange: (newValue) => setRegisterEmail(newValue),
                                    value: registerEmail
                                },
                                // Phone
                                {
                                    ...D_FieldModel_Phone(),
                                    inputId:"register-phone-input",
                                    onChange: (newValue) => setRegisterPhone(newValue),
                                    value: registerPhone,
                                },
                                // Company Name
                                {
                                    inputId: "register-company-input",
                                    type: FieldTypeEnum.Text,
                                    label: "Company (optional)",
                                    iconName: "User",
                                    value: registerCompany,
                                    onChange: (newValue) => setRegisterCompany(newValue)
                                },
                                // Password
                                {
                                    inputId:"register-password-input",
                                    type: FieldTypeEnum.Password,
                                    label: "Password",
                                    onChange: (newValue) => setRegisterPassword(newValue),
                                    value: registerPassword,
                                    validate: (v:any) => JC_Utils.stringNullOrEmpty(v)
                                                            ? "Enter a password."
                                                            : !JC_Utils_Validation.validPassword(v)
                                                                ? `Password invalid.`
                                                                : ""
                                },
                                // Password Requirements
                                {
                                    overrideClass: styles.passwordRequirementsField,
                                    inputId: "password-requirements",
                                    type: FieldTypeEnum.Custom,
                                    customNode: <JC_PasswordRequirements
                                                    key="password-requirements"
                                                    password={registerPassword}
                                                    showErrors={submitClicked}
                                                />
                                },
                                // Confirm Password
                                {
                                    inputId:"register-confirm-password-input",
                                    type: FieldTypeEnum.Password,
                                    label: "Confirm Password",
                                    onChange: (newValue) => setRegisterConfirmPassword(newValue),
                                    value: registerConfirmPassword,
                                    validate: (v:any) => JC_Utils.stringNullOrEmpty(v)
                                                            ? "Confirm the password."
                                                            : registerConfirmPassword != registerPassword
                                                                ? "Passwords do not match"
                                                                : ""
                                },
                                // Email Promotions
                                {
                                    overrideClass: styles.fieldOverride,
                                    inputId: "email-promotions-checkbox",
                                    type: FieldTypeEnum.Custom,
                                    customNode: <JC_Checkbox key="details-&-payment-title" label="Email Promotions" checked={emailPromotionsChecked} onChange={() => setEmailPromotionsChecked(!emailPromotionsChecked)} />
                                }
                            ]}
                        />





                    </div>
                }]}
            />
        </div>
    );
}

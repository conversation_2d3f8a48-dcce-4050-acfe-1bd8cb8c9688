import styles from "./JC_Header.module.scss";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import JC_HeaderButton from "./JC_HeaderButton";
import { auth } from "@/app/auth";

export default async function JC_Header() {

    const session = await auth();
    let loggedIn:boolean = !!session?.user;

    return (
        <React.Fragment>

            {/* Header */}
            <div className={styles.mainContainer} id="JC_header">

                {/* Logo + Account */}
                <div className={styles.logoAccountContainer}>

                    {/* Logo */}
                    <Image
                        src="/logos/Main [Simple].webp"
                        alt={"MainLogo"}
                        width={0}
                        height={0}
                        className={styles.logo}
                        unoptimized
                    />

                    {/* Account */}
                    {loggedIn && (
                        <div className={`${styles.checkoutAccountContainer}`}>
                            <Link href="/account" className={styles.navButton}>
                                <div className={styles.navButtonContent}>
                                    <Image
                                        src="/icons/User2.webp"
                                        alt="Account"
                                        width={0}
                                        height={0}
                                        className={styles.navIcon}
                                        unoptimized
                                    />
                                    <span className={styles.navLabel}>{session?.user.FirstName}</span>
                                </div>
                            </Link>
                        </div>
                    )}

                </div>

                {/* Navs */}
                <div className={styles.navsContainer}>

                    {/* Nav Buttons */}
                    <div className={styles.navButtons}>

                        <JC_HeaderButton linkToPage="customer" text="Customer" iconName="User2" iconWidth={23} />
                        <JC_HeaderButton linkToPage="property" text="Property" iconName="House" iconWidth={27} />
                        <JC_HeaderButton linkToPage="defects"  text="Defects"  iconName="Important" iconWidth={25} />

                        {/* Conditional User/Login Button */}
                        {loggedIn
                            ? <JC_HeaderButton linkToPage="account" text="User" iconName="User2" iconWidth={23} />
                            : <JC_HeaderButton linkToPage="login" text="Login" iconName="User2" iconWidth={23} />
                        }
                    </div>

                </div>

            </div>
        </React.Fragment>
    );

}